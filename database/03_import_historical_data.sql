-- =========== HISTORICAL DATA IMPORT SCRIPT ===========
-- PURPOSE: Imports the large historical CSV files (2019-2022 data)
-- RUN TIME: 2-3 HOURS (these are the big files)
-- WHAT IT DOES:
--   Imports 4 large historical files:
--   - EURUSD_Ticks_2019.12.31_2022.01.01.csv (2.5GB)
--   - GBPUSD_Ticks_2019.12.31_2022.01.01.csv (2.6GB) 
--   - USDCHF_Ticks_2019.12.31_2022.01.01.csv (1.5GB)
--   - USDJPY_Ticks_2019.12.31_2022.01.01.csv (1.9GB)

\c mktdata

\echo 'Starting historical data import (2019-2022)...'
\echo 'This will take 2-3 hours. You can monitor progress with each file.'

CALL process_tick_file('/tmp/EURUSD_Ticks_2019.12.31_2022.01.01.csv', 1);
CALL process_tick_file('/tmp/GBPUSD_Ticks_2019.12.31_2022.01.01.csv', 3);
CALL process_tick_file('/tmp/USDCHF_Ticks_2019.12.31_2022.01.01.csv', 4);
CALL process_tick_file('/tmp/USDJPY_Ticks_2019.12.31_2022.01.01.csv', 2);

\echo 'SUCCESS: Historical data import complete. Run 04_import_main_data.sql next.'
