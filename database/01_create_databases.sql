-- =========== DATABASE CREATION SCRIPT ===========
-- PURPOSE: Creates the mktdata and strategy_results databases
-- RUN TIME: ~5 seconds
-- WHAT IT DOES:
--   1. Terminates any existing connections to the databases
--   2. Drops existing databases (if they exist)
--   3. Creates fresh mktdata and strategy_results databases

\c postgres

-- Set session to auto-commit each command
\set AUTOCOMMIT on

-- Terminate existing connections to databases (if any)
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname IN ('mktdata', 'strategy_results') AND pid <> pg_backend_pid();

-- Drop existing databases for a clean slate, if they exist
DROP DATABASE IF EXISTS mktdata;
DROP DATABASE IF EXISTS strategy_results;

-- Create databases
CREATE DATABASE mktdata;
CREATE DATABASE strategy_results;

\echo 'SUCCESS: Databases created. Run 02_setup_tables.sql next.'
