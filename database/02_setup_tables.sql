-- =========== TABLE SETUP SCRIPT ===========
-- PURPOSE: Creates all tables, hypertables, and continuous aggregates
-- RUN TIME: ~10 seconds
-- WHAT IT DOES:
--   1. Sets up strategy_results database with strategies and backtest_reports tables
--   2. Sets up mktdata database with TimescaleDB extension
--   3. Creates ticks table and converts it to hypertable
--   4. Creates staging table for imports
--   5. Creates 9 OHLC continuous aggregates (10s, 1m, 5m, 10m, 15m, 30m, 1h, 4h, 1d)
--   6. Creates helper functions for data import

-- =========== SETUP STRATEGY_RESULTS DATABASE ===========
\c strategy_results

CREATE TABLE IF NOT EXISTS strategies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    parameters JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS backtest_reports (
    id SERIAL PRIMARY KEY,
    strategy_id INTEGER NOT NULL REFERENCES strategies(id),
    run_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    performance_summary JSONB NOT NULL,
    trades JSONB
);

-- =========== SETUP MKTDATA DATABASE ===========
\c mktdata

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Create the main ticks table
CREATE TABLE IF NOT EXISTS ticks (
    utc TIMESTAMPTZ NOT NULL,
    symbol INTEGER NOT NULL,
    ask DOUBLE PRECISION NOT NULL,
    bid DOUBLE PRECISION NOT NULL,
    ask_volume REAL,
    bid_volume REAL,
    CONSTRAINT ticks_symbol_utc_unique UNIQUE (symbol, utc)
);

-- Create a staging table for imports
CREATE TABLE IF NOT EXISTS ticks_staging (
    utc TIMESTAMPTZ,
    symbol INTEGER,
    ask DOUBLE PRECISION,
    bid DOUBLE PRECISION,
    ask_volume REAL,
    bid_volume REAL
);

-- Create the hypertable
SELECT create_hypertable('ticks', 'utc', 'symbol', 4, chunk_time_interval => INTERVAL '7 days', if_not_exists => TRUE);

-- Create all 9 OHLC continuous aggregates
DO $$
DECLARE
    intervals TEXT[] := ARRAY['10s', '1m', '5m', '10m', '15m', '30m', '1h', '4h', '1d'];
    cagg_name TEXT;
    cagg_interval TEXT;
    view_exists BOOLEAN;
BEGIN
    FOREACH cagg_interval IN ARRAY intervals
    LOOP
        cagg_name := 'ohlc_' || REPLACE(cagg_interval, ' ', '');
        
        -- Check if the materialized view already exists
        SELECT EXISTS (
            SELECT 1 FROM information_schema.views 
            WHERE table_name = cagg_name AND table_schema = 'public'
        ) INTO view_exists;
        
        -- Only create if it doesn't exist
        IF NOT view_exists THEN
            EXECUTE format(
                'CREATE MATERIALIZED VIEW %I
                 WITH (timescaledb.continuous) AS
                 SELECT symbol, time_bucket(%L, utc) AS bucket,
                        first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
                        first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
                        sum(ask_volume) as ask_volume, sum(bid_volume) as bid_volume, count(*) AS tick_count
                 FROM ticks GROUP BY symbol, bucket WITH NO DATA;',
                cagg_name, cagg_interval
            );
            RAISE NOTICE 'Created continuous aggregate: %', cagg_name;
        ELSE
            RAISE NOTICE 'Continuous aggregate already exists: %', cagg_name;
        END IF;
    END LOOP;
END;
$$;

-- Create helper functions for the import pipeline
CREATE OR REPLACE FUNCTION merge_staged_ticks() RETURNS void LANGUAGE plpgsql AS $$
BEGIN
    INSERT INTO ticks SELECT * FROM ticks_staging ON CONFLICT (symbol, utc) DO NOTHING;
    TRUNCATE ticks_staging;
END;
$$;

CREATE OR REPLACE PROCEDURE process_tick_file(file_path TEXT, symbol_id INTEGER)
LANGUAGE plpgsql AS $$
DECLARE
    file_exists BOOLEAN;
    rows_imported INTEGER;
BEGIN
    -- Check if file exists (this will raise an error if file doesn't exist)
    BEGIN
        EXECUTE format('COPY ticks_staging (utc, ask, bid, ask_volume, bid_volume) FROM %L WITH (FORMAT CSV, HEADER)', file_path);
        GET DIAGNOSTICS rows_imported = ROW_COUNT;
        RAISE NOTICE 'Imported % rows from %', rows_imported, file_path;
        
        UPDATE ticks_staging SET symbol = symbol_id WHERE symbol IS NULL;
        PERFORM merge_staged_ticks();
        RAISE NOTICE 'Successfully processed file: %', file_path;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE WARNING 'Failed to process file %: %', file_path, SQLERRM;
            -- Clear staging table in case of partial import
            TRUNCATE ticks_staging;
    END;
END;
$$;

\echo 'SUCCESS: All tables and functions created. Run individual import scripts next.'
