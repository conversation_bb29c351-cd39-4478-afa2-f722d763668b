-- =========== DATABASE CREATION SCRIPT ===========
-- This script ONLY creates the databases
-- Run time: ~5 seconds
-- Connect to the default 'postgres' db to perform these actions
\c postgres

-- Set session to auto-commit each command
\set AUTOCOMMIT on

-- Terminate existing connections to databases (if any)
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname IN ('mktdata', 'strategy_results') AND pid <> pg_backend_pid();

-- Drop existing databases for a clean slate, if they exist
DROP DATABASE IF EXISTS mktdata;
DROP DATABASE IF EXISTS strategy_results;

-- Create databases
CREATE DATABASE mktdata;
CREATE DATABASE strategy_results;

\echo 'Databases created successfully. Run 02_setup_tables.sql next.'

-- =========== PART 2: SETUP THE 'strategy_results' DATABASE ===========
\c strategy_results

CREATE TABLE IF NOT EXISTS strategies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    parameters JSONB NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS backtest_reports (
    id SERIAL PRIMARY KEY,
    strategy_id INTEGER NOT NULL REFERENCES strategies(id),
    run_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    performance_summary JSONB NOT NULL,
    trades JSONB
);


-- =========== PART 3: SETUP THE 'mktdata' DATABASE ===========
\c mktdata

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Create the main ticks table with the CORRECT column order and constraints
CREATE TABLE IF NOT EXISTS ticks (
    utc TIMESTAMPTZ NOT NULL,
    symbol INTEGER NOT NULL,
    ask DOUBLE PRECISION NOT NULL,
    bid DOUBLE PRECISION NOT NULL,
    ask_volume REAL,
    bid_volume REAL,
    CONSTRAINT ticks_symbol_utc_unique UNIQUE (symbol, utc)
);

-- Create a staging table for imports. NOTE: 'symbol' is NULLABLE here.
CREATE TABLE IF NOT EXISTS ticks_staging (
    utc TIMESTAMPTZ,
    symbol INTEGER,
    ask DOUBLE PRECISION,
    bid DOUBLE PRECISION,
    ask_volume REAL,
    bid_volume REAL
);

-- Create the hypertable using a more compatible syntax
SELECT create_hypertable('ticks', 'utc', 'symbol', 4, chunk_time_interval => INTERVAL '7 days', if_not_exists => TRUE);

-- Create all 9 OHLC continuous aggregates
DO $$
DECLARE
    intervals TEXT[] := ARRAY['10s', '1m', '5m', '10m', '15m', '30m', '1h', '4h', '1d'];
    cagg_name TEXT;
    cagg_interval TEXT;
    view_exists BOOLEAN;
BEGIN
    FOREACH cagg_interval IN ARRAY intervals
    LOOP
        cagg_name := 'ohlc_' || REPLACE(cagg_interval, ' ', '');

        -- Check if the materialized view already exists
        SELECT EXISTS (
            SELECT 1 FROM information_schema.views
            WHERE table_name = cagg_name AND table_schema = 'public'
        ) INTO view_exists;

        -- Only create if it doesn't exist
        IF NOT view_exists THEN
            EXECUTE format(
                'CREATE MATERIALIZED VIEW %I
                 WITH (timescaledb.continuous) AS
                 SELECT symbol, time_bucket(%L, utc) AS bucket,
                        first(ask, utc) AS open_ask, max(ask) AS high_ask, min(ask) AS low_ask, last(ask, utc) AS close_ask,
                        first(bid, utc) AS open_bid, max(bid) AS high_bid, min(bid) AS low_bid, last(bid, utc) AS close_bid,
                        sum(ask_volume) as ask_volume, sum(bid_volume) as bid_volume, count(*) AS tick_count
                 FROM ticks GROUP BY symbol, bucket WITH NO DATA;',
                cagg_name, cagg_interval
            );
        END IF;
    END LOOP;
END;
$$;


-- Create helper functions for the import pipeline
CREATE OR REPLACE FUNCTION merge_staged_ticks() RETURNS void LANGUAGE plpgsql AS $$
BEGIN
    INSERT INTO ticks SELECT * FROM ticks_staging ON CONFLICT (symbol, utc) DO NOTHING;
    TRUNCATE ticks_staging;
END;
$$;

-- Note: refresh_all_ohlc procedure removed as continuous aggregate refresh
-- will be done manually after the main setup is complete

CREATE OR REPLACE PROCEDURE process_tick_file(file_path TEXT, symbol_id INTEGER)
LANGUAGE plpgsql AS $$
DECLARE
    file_exists BOOLEAN;
    rows_imported INTEGER;
BEGIN
    -- Check if file exists (this will raise an error if file doesn't exist)
    BEGIN
        EXECUTE format('COPY ticks_staging (utc, ask, bid, ask_volume, bid_volume) FROM %L WITH (FORMAT CSV, HEADER)', file_path);
        GET DIAGNOSTICS rows_imported = ROW_COUNT;
        RAISE NOTICE 'Imported % rows from %', rows_imported, file_path;

        UPDATE ticks_staging SET symbol = symbol_id WHERE symbol IS NULL;
        PERFORM merge_staged_ticks();
        RAISE NOTICE 'Successfully processed file: %', file_path;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE WARNING 'Failed to process file %: %', file_path, SQLERRM;
            -- Clear staging table in case of partial import
            TRUNCATE ticks_staging;
    END;
END;
$$;

-- =========== PART 4: IMPORT ALL DATA FILES ===========
\echo 'Starting data import process...'

-- Import files with error handling
CALL process_tick_file('/tmp/EURUSD_Ticks_2019.12.31_2022.01.01.csv', 1);
CALL process_tick_file('/tmp/GBPUSD_Ticks_2019.12.31_2022.01.01.csv', 3);
CALL process_tick_file('/tmp/USDCHF_Ticks_2019.12.31_2022.01.01.csv', 4);
CALL process_tick_file('/tmp/USDJPY_Ticks_2019.12.31_2022.01.01.csv', 2);
CALL process_tick_file('/tmp/EURUSD_Ticks.csv', 1);
CALL process_tick_file('/tmp/GBPUSD_Ticks.csv', 3);
CALL process_tick_file('/tmp/USDCHF_Ticks.csv', 4);
CALL process_tick_file('/tmp/USDJPY_Ticks.csv', 2);
CALL process_tick_file('/tmp/EURUSD_Ticks_2025.07.01_2025.07.15.csv', 1);
CALL process_tick_file('/tmp/GBPUSD_Ticks_2025.07.01_2025.07.15.csv', 3);
CALL process_tick_file('/tmp/USDCHF_Ticks_2025.07.01_2025.07.15.csv', 4);
CALL process_tick_file('/tmp/USDJPY_Ticks_2025.07.01_2025.07.15.csv', 2);

\echo 'Data import complete. Refreshing continuous aggregates...'

-- =========== PART 5: COMPLETE SETUP ===========
\echo 'Database setup complete. Data imported successfully.'
\echo 'Note: Continuous aggregates will be refreshed automatically as new data arrives.'
\echo 'To manually refresh all continuous aggregates, run:'
\echo 'SELECT refresh_continuous_aggregate(view_name, NULL, NULL) FROM information_schema.views WHERE table_name LIKE ''ohlc_%'';'